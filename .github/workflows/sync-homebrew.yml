name: sync-homebrew

on:
  workflow_dispatch:
    inputs:
      tag-name:
        description: 'The git tag name to bump the cask to'
        required: false
        type: string
  workflow_run:
    workflows: ['release-assets']
    types:
      - completed

jobs:
  sync-homebrew:
    runs-on: macos-latest
    steps:
    - uses: mislav/bump-homebrew-formula-action@v3
      with:
        formula-name: escrcpy
        formula-path: Casks/escrcpy.rb
        tag-name: ${{ github.event.inputs.tag-name || github.event.release.tag_name }}
        download-url: https://github.com/viarotel-org/escrcpy/releases/download/v#{version}/Escrcpy-#{version}-mac-#{arch}.dmg
        homebrew-tap: viarotel-org/homebrew-escrcpy
        base-branch: main
        commit-message: |
          {{formulaName}} {{version}}

          Created by https://github.com/mislav/bump-homebrew-formula-action
      env:
        COMMITTER_TOKEN: ${{secrets.GH_TOKEN}}