name: sync-homebrew

on:
  workflow_dispatch:
  workflow_run:
    workflows: ['release-assets']
    types:
      - completed

jobs:
  sync-homebrew:
    runs-on: macos-latest
    steps:
    - uses: mislav/bump-homebrew-formula-action@v3
      with:
        formula-name: escrcpy
        formula-path: Casks/escrcpy.rb
        download-url: https://github.com/viarotel-org/escrcpy/releases/download/v#{version}/Escrcpy-#{version}-mac-#{arch}.dmg
        homebrew-tap: viarotel-org/escrcpy
        commit-message: |
          {{formulaName}} {{version}}

          Created by https://github.com/mislav/bump-homebrew-formula-action
      env:
        COMMITTER_TOKEN: ${{secrets.GH_TOKEN}}